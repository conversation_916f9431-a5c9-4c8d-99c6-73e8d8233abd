# Product Requirements Document (PRD) for World Info Optimizer Refactor

This document outlines the product requirements for refactoring and migrating the functionality of the legacy `Example_OldVersion.js` script into the new, modular TypeScript architecture located in `src/world_info_optimizer`.

## 1. Introduction

This PRD addresses the migration of all features from a legacy JavaScript file into a modern, maintainable, and scalable system. As a "brownfield" project, the primary goal is to achieve 100% feature parity with the old version while leveraging the benefits of the new architecture, such as type safety, modularity, and improved code organization.

## 2. Current System Analysis

### 2.1. Current State (Legacy System)
The existing system is a single, large JavaScript file (`Example_OldVersion.js`) implemented as an IIFE. It directly manipulates the DOM using jQuery and manages its entire state within a single `appState` object. Core logic, UI rendering, event handling, and API calls are tightly coupled.

### 2.2. Pain Points & Limitations
- **Poor Maintainability:** The monolithic nature makes it difficult to locate, understand, and modify code without risking unintended side effects.
- **Lack of Type Safety:** Being plain JavaScript, it is prone to runtime errors that could be caught at compile time with TypeScript.
- **Scalability Issues:** Adding new features is complex and increases the technical debt.
- **Tight Coupling:** The direct dependency on jQuery and global-like state management makes testing and modernization difficult.

### 2.3. Opportunities (New Architecture)
The new architecture in `src/world_info_optimizer/` provides:
- **Modularity:** Separation of concerns into `api`, `ui`, `store`, `core`, and `events`.
- **Type Safety:** TypeScript reduces bugs and improves developer experience.
- **State Management:** A dedicated `store.ts` provides a predictable and centralized state management solution.
- **Maintainability:** Clear structure makes it easier for developers to contribute and for the project to evolve.

## 3. Project Goals

- **Primary Goal:** Replicate all functionalities from `Example_OldVersion.js` in the new architecture, achieving 100% feature parity.
- **Secondary Goal:** Establish a robust and scalable foundation for future feature development.
- **Technical Goal:** Eliminate the dependency on the legacy script and jQuery, fully embracing the new TypeScript and modular patterns.

## 4. Functional Requirements (User Stories)

The new system must provide the following features, which are present in the legacy script:

### 4.1. Core Data Management
- **As a user, I want the application to load all necessary data upon opening,** including global/character regexes, all lorebooks, character-specific lorebooks, and the chat lorebook, so that I have a complete overview.
- **As a user, I want to be able to manually refresh all data** to sync with any changes made outside the extension.
- **As a user, I want the application to perform a partial refresh when I switch characters,** loading only character-specific data to improve performance. `Status: Implemented`.

### 4.2. UI & Navigation
- **As a user, I want a tabbed interface to switch between different views:** Global Lorebooks, Character Lorebooks, Chat Lorebook, Global Regex, and Character Regex.
- **As a user, I want the UI to clearly indicate when data is being loaded.**

### 4.3. Lorebook Management
- **As a user, I want to view all global lorebooks,** sorted by their enabled status and name.
- **As a user, I want to enable or disable global lorebooks** with a single click.
- **As a user, I want to create new, empty lorebooks.**
- **As a user, I want to rename existing lorebooks.**
- **As a user, I want to delete lorebooks** after a confirmation.
- **As a user, I want to view all lorebooks linked to the current character.**
- **As a user, I want to view the lorebook associated with the current chat session.**
- **As a user, I want a button to collapse all lorebooks currently associated with my character,** so I can quickly focus on other books. `Status: Implemented`.

### 4.4. Lorebook Entry Management
- **As a user, I want to view all entries within each lorebook.**
- **As a user, I want to create new entries within a specific lorebook.**
- **As a user, I want to edit all properties of an entry** (name, keys, content, etc.).
- **As a user, I want to delete one or more entries** from a lorebook.
- **As a user, I want to perform bulk operations on entries,** such as enabling "recursion prevention" for all entries in a book.

### 4.5. Regex Management
- **As a user, I want to view all global regular expressions.**
- **As a user, I want to view all character-specific regular expressions,** including those from the character card and the UI.
- **As a user, I want to edit existing regexes.**
- **As a user, I want to enable or disable individual regexes.**

### 4.6. Search and Replace
- **As a user, I want a powerful search bar to filter lorebooks and entries.**
- **As a user, I want to specify what fields to search in** (book name, entry name, keywords, content).
- **As a user, I want to perform a find-and-replace operation on the search results** across entry names, keywords, and content, after a confirmation prompt.

### 4.7. UI Feedback
- **As a user, I want to see modal dialogs for prompts, confirmations, and alerts.**
- **As a user, I want to see non-intrusive toast notifications** for successful operations (e.g., "Success!") and for ongoing processes (e.g., "Loading...").

---
**`Status: Completed`**
- **`Update:`** The Toast notification system has been fully implemented using SweetAlert2, managed via `ui/notifications.ts`. This replaces the previous `console.log` feedback, providing users with clear, non-intrusive visual confirmation for successful operations and status updates. This greatly enhances the user experience by making system responses immediate and easy to understand.

## 5. Non-Functional Requirements

- **Performance:** The new application's responsiveness and data loading times must be equal to or better than the legacy script. `Status: Improved`. Bulk operations (replace, delete, toggle) have been optimized to use parallel API requests, significantly improving performance when acting on items across multiple lorebooks.
- **Code Quality:** All new code must be written in TypeScript, follow the established architectural patterns, and be well-documented.
- **Compatibility:** The extension must remain fully compatible with the target versions of SillyTavern.

## 6. Assumptions & Constraints

- The existing `TavernAPI` wrapper in `api.ts` is the sole interface for interacting with SillyTavern's core functions.
- All DOM manipulation and rendering must be handled within `ui.ts` and be driven by state changes from `store.ts`.
- Direct calls to jQuery (`$`) are prohibited in the new implementation.

## 7. Roadmap & Milestones

- **Phase 1: Data Layer & Read-Only Views**
  - **Milestone 1.1:** Implement `loadAllData` in `core.ts` to fully populate the application state in `store.ts`. `Status: Completed`. The implementation is robust, with proper handling of loading and error states to prevent UI deadlocks.
  - **Milestone 1.2:** Implement the rendering logic in `ui.ts` to correctly display all 5 tabs (lorebooks and regexes) in a read-only state.
- **Phase 2: Lorebook & Entry CRUD**
  - **Milestone 2.1:** Implement full Create, Rename, Delete functionality for lorebooks.
  - **Milestone 2.2:** Implement full Create, Update, Delete functionality for lorebook entries.
- **Phase 3: Regex Management & Global Toggles**
  - **Milestone 3.1:** Implement editing and enabling/disabling of regexes.
  - **Milestone 3.2:** Implement the toggle for enabling/disabling global lorebooks.
- **Phase 4: Advanced Features**
  - **Milestone 4.1:** Implement the search and filtering functionality.
  - **Milestone 4.2:** Implement the find-and-replace functionality. `Status: Completed`. The UI and core logic for bulk find-and-replace are now implemented.
  - **Milestone 4.3:** Implement advanced UI interactions. `Status: Completed`. Multi-select mode for bulk operations (delete, enable/disable) and collapsible book views are now implemented.
- **Phase 5: Finalization**
  - **Milestone 5.1:** Conduct thorough testing to ensure 100% feature parity.
  - **Milestone 5.2:** Deprecate and remove `Example_OldVersion.js`.
