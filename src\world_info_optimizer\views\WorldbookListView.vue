<template>
  <div class="worldbook-list-view">
    <!-- 状态显示 -->
    <n-spin v-if="uiStore.isLoading" description="Loading worldbooks..." size="large" />
    <n-empty
      v-else-if="uiStore.error"
      description="Failed to load data"
    >
      <template #extra>
        <p>{{ uiStore.error }}</p>
        <n-button @click="fetchData" type="primary">Retry</n-button>
      </template>
    </n-empty>
    <n-empty
      v-else-if="worldbookStore.totalBooks === 0"
      description="No worldbooks found."
    >
      <template #extra>
        <n-button type="primary">Create New Worldbook</n-button>
      </template>
    </n-empty>

    <!-- 数据列表 -->
    <n-list v-else bordered clickable>
      <template #header>
        <div class="list-header">
          <h2>All Worldbooks ({{ worldbookStore.totalBooks }})</h2>
          <div>
            <!-- 新建和刷新按钮 -->
            <n-button type="primary" style="margin-right: 8px;">New Book</n-button>
            <n-button @click="fetchData">
              <template #icon><n-icon :component="RefreshIcon" /></template>
              Refresh
            </n-button>
          </div>
        </div>
      </template>

      <n-list-item v-for="book in worldbookStore.worldbookList" :key="book.name" @click="navigateToDetail(book.name)">
        <template #prefix>
          <n-icon :component="BookIcon" size="24" />
        </template>
        
        <n-thing :title="book.name" :description="`${book.entryCount} entries`" />

        <template #suffix>
          <!-- 导航和操作按钮将在这里添加 -->
          <n-button text>
             <template #icon><n-icon :component="ChevronRightIcon" /></template>
          </n-button>
        </template>
      </n-list-item>
    </n-list>
  </div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue';
import {
  NSpin,
  NEmpty,
  NButton,
  NList,
  NListItem,
  NIcon,
  NThing,
} from 'naive-ui';
import { useWorldbookStore } from '../store/useWorldbookStore';
import { useRouter } from 'vue-router';
import { useUIStateStore } from '../store/useUIStateStore';
import {
    BookOutline as BookIcon,
    ChevronForwardOutline as ChevronRightIcon,
    RefreshOutline as RefreshIcon,
} from '@vicons/ionicons5';


const worldbookStore = useWorldbookStore();
const uiStore = useUIStateStore();
const router = useRouter();

const fetchData = () => {
  worldbookStore.fetchWorldbookList();
};

const navigateToDetail = (bookName: string) => {
  router.push({ name: 'detail', params: { name: bookName } });
};

// 组件挂载时自动获取数据
onMounted(fetchData);

</script>

<style scoped>
.worldbook-list-view {
  padding: 8px;
}
.list-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.n-spin, .n-empty {
  height: 300px;
  display: flex;
  justify-content: center;
  align-items: center;
}
</style>