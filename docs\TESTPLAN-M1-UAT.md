# UAT 测试计划: 里程碑 1 - 架构验证与核心数据显示

**版本**: 1.0
**状态**: 待执行
**测试负责人**: QA / PO

---

## 1. 测试目标

本用户验收测试 (UAT) 旨在验证“里程碑 1”交付的只读 MVP (Minimum Viable Product) 是否满足核心用户需求和质量标准。

**关键验证点**:

- 应用能否成功启动并加载初始数据。
- 核心数据显示是否准确、完整。
- 用户基本导航流程是否顺畅、符合预期。
- 系统的边界状态（加载、空、错误）处理是否健壮。
- M1 后的重构是否对用户体验无负面影响。

**相关文档**:

- [开发日志 DEVLOG-M1.md](./DEVLOG-M1.md)
- [产品需求文档 PRD.md](./PRD.md)
- [UX 设计规范 UX-Spec.md](./UX-Spec.md)

---

## 2. 测试环境

- **浏览器**: 最新版 Chrome / Firefox
- **数据源**: `MockBridgeAPI` (当前阶段)
- **前置条件**: 确保项目已成功编译并运行在本地开发服务器上。

---

## 3. 测试用例

| 用例 ID       | 测试模块            | 测试场景                | 测试步骤                                                                                                   | 预期结果                                                                                                                                                                                                                           | 测试结果 (Pass/Fail) | 备注                |
| :------------ | :------------------ | :---------------------- | :--------------------------------------------------------------------------------------------------------- | :--------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | :------------------- | :------------------ |
| **TC-M1-001** | **应用加载**        | **正常加载**            | 1. 访问应用首页 (路径 `/`)。 <br> 2. 观察界面。                                                            | 1. 界面首先显示 “Loading worldbooks...” 的加载指示器。 <br> 2. 短暂延迟后，加载指示器消失，显示 Worldbook 列表。 <br> 3. 浏览器控制台无错误信息。                                                                                  |                      |                     |
| **TC-M1-002** | **列表页视图**      | **数据显示 - 正常情况** | 1. 应用加载成功后，查看 Worldbook 列表。                                                                   | 1. 列表正确显示所有 `MockBridgeAPI` 中的 Worldbook，包括: "My Awesome Worldbook", "Magic Spells", "Empty Book", "Book of Errors"。 <br> 2. 每个列表项都正确显示其名称和条目数量（例如，"My Awesome Worldbook" 显示 "2 entries"）。 |                      |                     |
| **TC-M1-003** | **列表页视图**      | **数据显示 - 空状态**   | 1. (需修改代码) 临时修改 `MockBridgeAPI`，使其 `getWorldbookList` 返回一个空数组 `[]`。 <br> 2. 刷新应用。 | 1. 界面不显示列表，而是显示一个空状态提示组件。 <br> 2. 提示文案应为 “No worldbooks found.”。 <br> 3. 包含一个 “Create New Worldbook” 的操作按钮。                                                                                 |                      | 需要开发人员协助    |
| **TC-M1-004** | **导航**            | **从列表页到详情页**    | 1. 在 Worldbook 列表页，点击 "My Awesome Worldbook" 列表项。                                               | 1. 应用平滑切换到详情页视图。 <br> 2. 浏览器地址栏的 URL hash 变为 `#/detail/My%20Awesome%20Worldbook`。 <br> 3. 详情页标题正确显示为 "My Awesome Worldbook"。                                                                     |                      |                     |
| **TC-M1-005** | **详情页视图**      | **数据显示 - 正常情况** | 1. 进入 "My Awesome Worldbook" 的详情页。 <br> 2. 观察界面。                                               | 1. 界面首先显示 “Loading entries...” 的加载指示器。 <br> 2. 加载结束后，显示一个可折叠的条目列表。 <br> 3. 列表正确显示该书的 2 个条目："aragorn" 和 "gondor"。 <br> 4. 每个条目都包含其内容预览和右侧的编辑/删除图标按钮。        |                      |                     |
| **TC-M1-006** | **详情页视图**      | **数据显示 - 空条目**   | 1. 从列表页点击进入 "Empty Book" 的详情页。                                                                | 1. 界面不显示条目列表，而是显示一个空状态提示。 <br> 2. 提示文案应为 “This worldbook has no entries.”。 <br> 3. 包含一个 “Create the First Entry” 的操作按钮。                                                                     |                      |                     |
| **TC-M1-007** | **详情页视图**      | **错误处理**            | 1. 从列表页点击进入 "Book of Errors" 的详情页。                                                            | 1. 界面不显示条目列表，而是显示一个错误状态提示。 <br> 2. 错误信息应包含 “Permission denied to read this cursed book.”。 <br> 3. 包含一个 “Retry” 按钮，点击后应能重新触发数据加载。                                               |                      |                     |
| **TC-M1-008** | **导航**            | **从详情页返回列表页**  | 1. 在任意 Worldbook 的详情页。 <br> 2. 点击页面左上角的 “<” 返回按钮。                                     | 1. 应用平滑切换回 Worldbook 列表页视图。 <br> 2. 浏览器地址栏的 URL hash 变回 `#/`。 <br> 3. 列表页的数据应保持原样（或从缓存加载），无需重新显示加载动画。                                                                        |                      |                     |
| **TC-M1-009** | **性能 (重构验证)** | **列表加载效率**        | 1. 打开浏览器开发者工具的网络 (Network) 面板。 <br> 2. 刷新应用首页。                                      | 1. 整个列表加载过程，应用只调用 `BridgeAPI` 的 `getWorldbookList` **一次**。 <br> 2. 绝不应该出现多次 `loadWorldbook` 的调用。                                                                                                     |                      | 验证 N+1 问题已修复 |
| **TC-M1-010** | **架构 (重构验证)** | **依赖注入**            | 1. 检查代码 [`index.ts`](../src/world_info_optimizer/index.ts)。                                             | 1. `bridge` 实例是通过调用 `createBridge()` 创建的。 <br> 2. 该实例通过 `worldbookStore.setBridge(bridge)` 的方式注入到 store 中。 <br> 3. 代码中不应再有对 `bridge` 单例的直接导入。                                              |                      | 代码审查项          |
