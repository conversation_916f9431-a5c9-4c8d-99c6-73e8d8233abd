{
  "compilerOptions": {
    "types": [
      "jquery",
      "jqueryui",
      "lodash",
      "toastr",
      "type-fest",
      "yaml",
      "zod",
    ],
    "target": "ESNext",
    "module": "ESNext",
    "outDir": "dist",
    "baseUrl": "src",
    "paths": {
      "@/*": [
        "./*",
      ]
    },
    "moduleDetection": "auto",
    "moduleResolution": "bundler",
    "allowUmdGlobalAccess": true,
    "allowSyntheticDefaultImports": true,
    "strictBindCallApply": true,
    "allowJs": true,
    "checkJs": false,
    "sourceMap": true,
    "strict": true,
    "noUnusedLocals": true,
    "noUnusedParameters": true,
    "noImplicitReturns": true,
    "noFallthroughCasesInSwitch": true,
    "esModuleInterop": true,
    "forceConsistentCasingInFileNames": true
  },
  "include": [
    "@types",
    "src",
    "global.d.ts", "初始模板/example", "初始模板/脚本示例/全自动总结测试版_gemini.js",
  ],
  "exclude": [
    "**/dist/**",
    "**/node_modules/**",
  ]
}