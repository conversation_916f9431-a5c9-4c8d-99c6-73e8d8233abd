import { defineStore } from 'pinia';
import { ref, computed } from 'vue'; // 显式导入 ref 和 computed
import { IBridgeAPI, Worldbook, WorldbookListItem } from '../api/bridge';
import { useUIStateStore } from './useUIStateStore';

// 使用函数式 Pinia store 写法，以便于依赖注入
export const useWorldbookStore = defineStore('worldbook', () => {
    // 这是一个 state-like ref
    const worldbookList = ref<WorldbookListItem[]>([]);
    const currentWorldbook = ref<Worldbook | null>(null);

    // 这是一个 getter-like computed
    const totalBooks = computed(() => worldbookList.value.length);
    
    // 这是 action-like function
    // 注意：我们在这里假设 bridge 实例是通过某种方式注入的。
    // 在 main.ts 中，我们会处理这个问题。
    // 为了临时解决编译问题，我们先使用一个占位符。
    let bridge: IBridgeAPI;
    function setBridge(api: IBridgeAPI) {
        bridge = api;
    }

    async function fetchWorldbookList() {
        const uiStore = useUIStateStore();
        uiStore.setLoading(true);
        uiStore.setError(null);

        try {
            const listItems = await bridge.getWorldbookList();
            worldbookList.value = listItems;
        } catch (error) {
            const message = error instanceof Error ? error.message : 'An unknown error occurred';
            uiStore.setError(`Failed to fetch worldbook list: ${message}`);
            console.error(error);
        } finally {
            uiStore.setLoading(false);
        }
    }
    
    async function fetchWorldbookDetail(name: string) {
        const uiStore = useUIStateStore();
        uiStore.setLoading(true);
        uiStore.setError(null);
        currentWorldbook.value = null;

        try {
            const book = await bridge.loadWorldbook(name);
            if (book) {
                currentWorldbook.value = book;
            } else {
                throw new Error(`Worldbook "${name}" not found or failed to load.`);
            }
        } catch (error) {
            const message = error instanceof Error ? error.message : 'An unknown error occurred';
            uiStore.setError(`Failed to fetch worldbook details: ${message}`);
            console.error(error);
        } finally {
            uiStore.setLoading(false);
        }
    }

    return {
        worldbookList,
        currentWorldbook,
        totalBooks,
        setBridge,
        fetchWorldbookList,
        fetchWorldbookDetail,
    };
});