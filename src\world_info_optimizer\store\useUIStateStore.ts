import { defineStore } from 'pinia';

// 这是一个骨架 store，用于管理应用的纯 UI 状态，例如加载状态、当前激活的标签页等。

export type ViewName = 'list' | 'detail';

export const useUIStateStore = defineStore('uiState', {
  state: () => ({
    isLoading: false,
    activeTab: 'global', // 默认标签页，可选值: 'global', 'character', 'chat'
    error: null as string | null,
    currentView: 'list' as ViewName,
    // 用于存储从列表页传递到详情页的上下文信息
    navigationContext: null as any,
  }),

  actions: {
    setLoading(status: boolean) {
      this.isLoading = status;
    },
    setError(errorMessage: string | null) {
      this.error = errorMessage;
    },
    setActiveTab(tabName: 'global' | 'character' | 'chat') {
        this.activeTab = tabName;
    },
    /**
     * 导航到指定的视图。
     * @param view - 目标视图的名称 ('list' 或 'detail')。
     * @param context - (可选) 传递给目标视图的上下文数据，例如 worldbook 名称。
     */
    navigateTo(view: ViewName, context: any = null) {
      this.currentView = view;
      this.navigationContext = context;
    }
  },
});