# 技术规格书 (Tech Spec) - Worldbook 管理器 (新项目)

**版本**: 1.2
**状态**: 架构决策已同步

---

## 1. 概述

本文档定义了“Worldbook 管理器”新项目的技术架构、选型和开发规范。旨在为工程团队提供一份清晰的行动蓝图，确保项目具备高性能、高可维护性和高扩展性。

本文档的核心思想大量借鉴了项目前身 (`vue-migration-plan.md`) 中经过深入论证的顶级工程实践。

---

## 相关文档

*   **[产品需求文档 (PRD)](./PRD.md)**: 本技术方案所服务的业务需求。
*   **[项目路线图 (Roadmap)](./Roadmap.md)**: 本技术方案的实施路径。
*   **[UX 设计与交互规范 (UX Spec)](./UX-Spec.md)**: UI构建策略的具体设计指导。
*   **[API 设计文档 (BridgeAPI)](./BridgeAPI.md)**: 核心架构原则 `BridgeAPI` 的详细契约。

---

## 2. 最终技术栈

| 类别 | 技术/工具 | 选用理由 |
| :--- | :--- | :--- |
| **核心框架** | Vue 3 + Vite + TypeScript | 现代化的前端开发基石，提供卓越的开发体验、类型安全和构建性能。 |
| **UI 框架** | **Naive UI** | **开发效率最大化**。提供丰富、高质量且性能优越的组件，一流的TS支持，以及强大的主题定制能力。 |
| **状态管理** | Pinia | Vue官方推荐的状态管理库，轻量、直观，与TypeScript和Vue DevTools完美集成。 |
| **单元/组件测试** | Vitest + Vue Testing Library | 与Vite生态无缝集成，提供快速、简洁的测试体验。 |
| **E2E 测试** | Playwright | 功能强大且可靠的端到端测试框架，用于验证关键用户流程。 |
| **代码规范** | ESLint + Prettier | 保证代码风格统一，在编码阶段预防潜在错误。 |

---

## 3. UI 构建策略：全面拥抱 Naive UI

**核心策略**: 将 **Naive UI** 作为项目的UI构建基石。我们不重复造轮子，将开发重心完全放在业务逻辑实现上。关于如何具体使用这些组件，请参阅 **[`UX 设计与交互规范`](./UX-Spec.md#5-组件应用规范-component-usage-guide)**。

1.  **全面采用 Naive UI 组件**: 项目中所有基础UI元素（按钮、输入框、模态框、标签页等）都必须使用 Naive UI 对应的组件。
2.  **全局主题配置**: 在应用根组件 (`App.vue`) 中配置 `n-config-provider`，统一设置暗色模式等基础主题。
3.  **精简自定义CSS**: 自定义CSS仅用于：
    *   应用级的宏观布局（如主面板结构）。
    *   覆盖 Naive UI 中极少数不符合需求的特定样式。
    *   定义全局辅助类。
4.  **废弃 `SweetAlert2`**: 使用 Naive UI 自带的 `Dialog` 和 `Message` API 来处理所有模态框和通知，以增强应用的独立性和稳定性。

---

## 4. 应用架构设计

我们将采用经过验证的、层次分明的应用架构，其核心是**接口先行**与**关注点分离**。

### 4.1 项目文件结构

为保持清晰和模块化，所有项目的源代码都将位于 `src/world_info_optimizer` 目录下。最终的构建产物也将从该目录生成。

```
src/
└── world_info_optimizer/
    ├── api/          # BridgeAPI 适配器层
    ├── assets/       # 静态资源
    ├── components/   # 可复用的Vue组件
    ├── store/        # Pinia状态管理
    ├── views/        # 页面级视图组件
    ├── App.vue       # 应用根组件
    └── index.ts      # 应用入口
```

### 4.2 核心架构原则: 防腐层 (`BridgeAPI`)

**此为本架构的基石。**

所有与宿主环境 (SillyTavern) 的通信，都**必须**通过一个精确定义的 `BridgeAPI` 接口。这个接口是我们的应用与外部世界之间的唯一契约。

*   **目的**: 作为应用的**防腐层 (Anti-Corruption Layer)**，它将我们的 Vue 应用与外部环境的实现细节和潜在变化完全隔离。
*   **实现方式 - 适配器模式**: `BridgeAPI` 将通过**适配器模式 (Adapter Pattern)** 实现。我们不会直接处理底层的 `postMessage` 通信，而是会创建一个适配器来封装 SillyTavern 宿主环境已经提供的全局 `SillyTavern` 对象。
*   **职责**:
    *   **封装**: 将所有对 `SillyTavern.loadWorldInfo()`, `SillyTavern.saveWorldInfo()` 等方法的直接调用封装在适配器内部。
    *   **响应化**: 监听 `tavern_events.WORLDINFO_UPDATED` 等事件，并将其转化为 Pinia Store 的更新操作，从而驱动 UI 自动刷新。
    *   **解耦**: 确保 Vue 组件和 Pinia Store 只依赖于我们定义的 `BridgeAPI` 接口，而完全不知道全局 `SillyTavern` 对象的存在。

> 关于 `BridgeAPI` 的完整方法、数据结构和事件模型的详细定义，请参阅 **[`BridgeAPI 设计文档`](./BridgeAPI.md)**。

### 4.2 组件化拆分策略

遵循“原子设计”思想，并根据“列表 -> 详情”的新流程，将UI拆分为以下层次：

*   **布局组件 (Layouts)**: 负责搭建应用的宏观结构，如 `WioPanel.vue` (整体面板), `WioToolbar.vue` (工具栏)。
*   **视图组件 (Views)**: 对应各个主要功能页面，如 `WorldbookListView.vue` (列表页), `WorldbookDetailView.vue` (详情页)。
*   **业务/原子组件 (Components)**: 可复用的最小功能单元，如 `WorldbookListItem.vue` (列表项), `EntryItem.vue` (详情页中的条目行)。

### 4.3 状态管理 (Pinia): 按领域拆分

为保证长期可维护性和扩展性，我们将从一开始就采用**按领域拆分**的 Pinia Store 模式。

*   **`useWorldbookStore`**: 负责管理所有与 Worldbook 及其条目相关的核心数据和业务逻辑（如 `allWorldbooks`, `currentWorldbook`, `fetchWorldbooks`, `deleteEntry` 等）。
*   **`useUIStateStore`**: 负责管理纯粹的UI状态和交互逻辑（如 `activeTab`, `isLoading`, `isMultiSelectMode`, `setLoadingState` 等）。

这种模式确保了**高内聚、低耦合**，使得代码更容易理解、测试和扩展。

---

## 5. 质量保证策略

*   **单元测试**: 所有核心业务逻辑（尤其是在 Pinia `actions` 中）都必须有单元测试覆盖。
*   **组件测试**: 复杂的业务组件应编写组件测试，验证其不同 props 下的渲染和交互行为。
*   **E2E 测试**: 针对核心的用户流程（如：新建 Worldbook -> 查看详情 -> 新建条目 -> 编辑条目 -> 删除）编写 E2E 测试，作为发布的最终门禁。
*   **静态代码分析**: 强制执行 ESLint 和 Prettier 规则，不符合规范的代码不允许提交。

---

## 6. 风险分析与缓解

| 风险类别 | 具体风险描述 | 缓解策略 |
| :--- | :--- | :--- |
| **技术风险** | 样式冲突 (SillyTavern全局样式污染) | 1. **强制使用 `<style scoped>`**。<br>2. 采用BEM命名法辅助调试。<br>3. 在根组件中引入轻量级CSS Reset。 |
| **技术风险** | 大数据量下的性能瓶颈 | 针对长列表（Worldbook 列表、条目列表），在架构上预留**虚拟滚动**的实现接口。 |
| **集成风险** | 宿主环境API变更 | 所有对宿主环境的调用都必须通过一个统一的 `BridgeAPI` 模块。该模块作为**防腐层**，隔离外部变化。 |