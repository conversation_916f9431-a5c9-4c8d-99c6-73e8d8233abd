# 项目路线图 (Roadmap) - Worldbook 管理器 (新项目)

**版本**: 2.0
**状态**: PO 已审查并增强

---

## 1. 概述

本文档为“Worldbook 管理器”新项目提供了高阶的开发路线图。它将产品功能分解为一系列更小、可独立测试的开发节点，旨在以一种循序渐进、**价值驱动**的方式来构建产品。

每个节点都代表一个可交付、可被PO验收的用户价值闭环。

---

## 相关文档

*   **[产品需求文档 (PRD)](./PRD.md)**: 本路线图所实现功能的需求来源。
*   **[技术规格书 (Tech Spec)](./TechSpec.md)**: 指导本路线图技术实现的架构蓝图。
*   **[UX 设计与交互规范 (UX Spec)](./UX-Spec.md)**: 为各节点UI/UX实现提供具体的设计依据。
*   **[API 设计文档 (BridgeAPI)](./BridgeAPI.md)**: 里程碑1中核心任务的详细定义。

---

## 2. 里程碑规划

### **里程碑 1: 架构验证与核心数据显示 (MVP Core)**

**目标**: 验证核心架构，搭建应用骨架，并成功加载和**只读**展示核心数据，为用户提供最基础的浏览功能。

*   **[X] 节点 1.0: 架构核心 - API契约定义与验证 (最高优先级)**
    *   **负责人 (Owner)**: **Architect**
    *   **关联用户故事**: 2.1 数据管理
    *   **任务**: 在 `src/world_info_optimizer/api/bridge.ts` 中精确定义 `BridgeAPI` 接口和适配器。创建详细的 [`BridgeAPI.md`](./BridgeAPI.md) 文档。
    *   **验收标准**: [`BridgeAPI.md`](./BridgeAPI.md) 文档清晰、完备，并通过最小化原型验证了核心API的连通性。
*   **[ ] 节点 1.1: 行走的骨架 (Walking Skeleton)**
    *   **负责人 (Owner)**: **Architect / Developer**
    *   **关联用户故事**: 2.2 界面与导航
    *   **任务**: 初始化项目，集成技术栈，创建主面板、标签页和空的视图容器。建立Pinia Store结构。
    *   **验收标准**: 应用可以无错启动，界面显示基本的布局框架，状态管理devtools可见。
*   **[ ] 节点 1.2: Worldbook 列表只读显示**
    *   **负责人 (Owner)**: **Developer / UX Expert**
    *   **关联用户故事**: “作为用户, 我希望有一个清晰的导航流程: 首先看到一个 Worldbook 列表...”
    *   **任务**: 调用 `BridgeAPI` 获取数据，在 `WorldbookListView` 中渲染列表。处理加载中、空、错误状态。具体布局参考: [`UX-Spec.md - 列表页线框图`](./UX-Spec.md#61-列表页-worldbook-list-view)。
    *   **验收标准**: 用户能看到 Worldbook 列表，且各种边界状态（加载、空、错误）均有清晰的UI展示。
*   **[ ] 节点 1.3: 导航至详情页**
    *   **负责人 (Owner)**: **Developer**
    *   **关联用户故事**: “...点击其中一项后，再进入该 Worldbook 的条目详情页面。”
    *   **任务**: 实现视图切换，点击列表项能跳转到详情页，并传递正确的 Worldbook 标识。
    *   **验收标准**: 可以从列表页成功跳转到详情页，详情页能正确显示所选 Worldbook 的标题。
*   **[ ] 节点 1.4: Worldbook 条目只读显示**
    *   **负责人 (Owner)**: **Developer / UX Expert**
    *   **关联用户故事**: “作为用户, 我希望在详情页可以对 Worldbook 条目进行查看...” (隐含需求)
    *   **任务**: 在详情页中，获取并渲染该 Worldbook 下的所有条目列表。具体布局参考: [`UX-Spec.md - 详情页线框图`](./UX-Spec.md#62-详情页-worldbook-detail-view)。
    *   **验收标准**: 用户进入详情页后，能看到该 Worldbook 的所有条目列表。

---

### **里程碑 2: 核心编辑功能闭环**

**目标**: 逐一实现对 Worldbook 和条目的增删改查功能，让应用变得“可用”。

*   **[ ] 节点 2.1: Worldbook 的增删改**
    *   **负责人 (Owner)**: **Developer**
    *   **关联用户故事**: "作为用户, 我希望在列表页可以对 Worldbook 进行完整的创建、重命名和删除操作。"
    *   **任务**: 在列表页实现新建、重命名、删除 Worldbook 的功能，并处理所有相关的API调用和UI反馈。
    *   **验收标准**: 用户可以通过UI在列表页完成对Worldbook的增、删、改操作，且数据持久化正确。
*   **[ ] 节点 2.2: Worldbook 条目的增删改**
    *   **负责人 (Owner)**: **Developer**
    *   **关联用户故事**: "作为用户, 我希望在详情页可以对 Worldbook 条目进行完整的创建、编辑和删除操作。"
    *   **任务**: 在详情页实现新建、编辑、删除条目的功能，包括编辑器模态框的开发。
    *   **验收标准**: 用户可以通过UI在详情页完成对条目的增、删、改操作，数据持久化正确。
*   **[ ] 节点 2.3: 状态同步**
    *   **负责人 (Owner)**: **Developer**
    *   **关联用户故事**: "作为用户, 我希望可以在列表页一键启用或禁用..." & "批量启用或禁用"
    *   **任务**: 实现启用/禁用 Worldbook（在列表页）和启用/禁用条目（在详情页）的功能。
    *   **验收标准**: 所有的状态切换开关都能正常工作，并能正确地持久化状态。
*   **[ ] 节点 2.4: 条目内容折叠**
    *   **负责人 (Owner)**: **Developer / UX Expert**
    *   **关联用户故事**: "作为用户, 我希望可以方便地折叠或展开单个条目内容..."
    *   **任务**: 在详情页，为每个条目实现内容区域的折叠和展开功能，并记住状态。
    *   **验收标准**: 用户可以点击展开/收起每个条目的详细内容，刷新后状态应被保留。

---

### **里程碑 3: 效率工具与高级功能**

**目标**: 引入搜索、多选和批量操作，让应用从“可用”进化到“好用”。

*   **[ ] 节点 3.1: 全局搜索**
    *   **负责人 (Owner)**: **Developer**
    *   **关联用户故事**: "作为用户, 我希望有一个强大的搜索栏..."
    *   **任务**: 实现搜索功能。在列表页，搜索框能过滤 Worldbook；在详情页，能过滤条目。
    *   **验收标准**: 在列表页和详情页，搜索功能都能实时、准确地过滤列表内容。
*   **[ ] 节点 3.2: 多选模式**
    *   **负责人 (Owner)**: **Developer / UX Expert**
    *   **关联用户故事**: "作为用户, 我希望可以对多个条目进行批量操作..."
    *   **任务**: 在详情页实现“进入/退出多选模式”的功能，并允许用户通过复选框选择多个条目。
    *   **验收标准**: 用户可以平滑地进入/退出多选模式，复选框功能正常，并有明确的视觉反馈。
*   **[ ] 节点 3.3: 批量操作**
    *   **负责人 (Owner)**: **Developer**
    *   **关联用户故事**: "...例如批量启用或禁用。"
    *   **任务**: 基于多选结果，实现批量删除、批量启用、批量禁用功能。
    *   **验收标准**: 用户可以对选中的条目成功执行批量操作，且有清晰的操作反馈。
*   **[ ] 节点 3.4: 批量搜索与替换**
    *   **负责人 (Owner)**: **Developer**
    *   **关联用户故事**: "作为用户, 我希望可以在搜索结果中执行查找和替换操作。"
    *   **任务**: 在详情页实现批量查找并替换所有（或选中）条目内容的功能，需有二次确认。
    *   **验收标准**: 批量替换功能符合预期，二次确认机制能有效防止误操作。

---

### **里程碑 4: 润色、测试与发布**

**目标**: 全面测试应用，修复缺陷，优化性能和体验，为正式发布做准备。

*   **[ ] 节点 4.1: 全面功能回归测试**
    *   **负责人 (Owner)**: **QA / PO**
    *   **关联用户故事**: 所有用户故事
    *   **任务**: 根据 PRD 的功能清单，进行端到端的完整功能验证，确保所有功能符合验收标准。
    *   **验收标准**: 所有测试用例均通过，无重大或阻塞性缺陷。
*   **[ ] 节点 4.2: 性能与体验优化**
    *   **负责人 (Owner)**: **Developer / UX Expert**
    *   **关联用户故事**: 非功能性需求
    *   **任务**: 针对大数据量场景进行性能分析和优化，并打磨所有交互流程、提示文案和视觉细节。
    *   **验收标准**: 应用在处理100个Worldbook、每个包含100个条目时，核心操作响应时间小于500ms。
*   **[ ] 节点 4.3: 最终发布准备**
    *   **负责人 (Owner)**: **PO**
    *   **关联用户故事**: 项目愿景
    *   **任务**: 编写用户文档，进行 Beta 测试，收集并修复最终反馈。
    *   **验收标准**: 项目已准备就绪，可以向最终用户发布。