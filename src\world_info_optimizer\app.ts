import { createApp } from 'vue';
import { createPinia } from 'pinia';
import App from './App.vue';
import router from './router';
import { createBridge } from './api/bridge';
import { useWorldbookStore } from './store/useWorldbookStore';

export function init(mountPoint: string | HTMLElement) {
  // 1. 创建 BridgeAPI 实例
  //    这里可以根据环境变量或其他配置选择 'mock' 或 'real'
  const bridge = createBridge('mock');

  // 2. 创建 Pinia 实例
  const pinia = createPinia();

  // 3. 创建 Vue 应用实例
  const app = createApp(App);

  // 4. 使用插件
  app.use(pinia);
  app.use(router);

  // 5. 执行依赖注入
  //    在 store 被使用前，将 bridge 实例注入
  const worldbookStore = useWorldbookStore();
  worldbookStore.setBridge(bridge);

  // 6. 挂载应用
  app.mount(mountPoint);

  return app;
}