import { init as initVueApp } from './app';

// --- 全局API变量 ---
let SillyTavern_API: any;
let TavernHelper_API: any;
let jQuery_API: any;

// --- 配置常量 ---
const SCRIPT_ID = 'world-info-optimizer';
const MENU_ITEM_ID = `${SCRIPT_ID}-menu-item`;
const MOUNT_POINT_ID = `${SCRIPT_ID}-mount-point`;
const BUTTON_TEXT = '世界书管理器';
const BUTTON_TOOLTIP = '打开世界书管理器';

/**
 * 打开优化器弹窗
 */
function openOptimizerPopup() {
  // 此时 onReady 已经确保了 callGenericPopup 是可用的
  if (!SillyTavern_API || !SillyTavern_API.callGenericPopup) {
    console.error('[WIO] 致命错误: openOptimizerPopup 被调用，但 SillyTavern_API.callGenericPopup 不存在!');
    return;
  }

  // 弹窗的 HTML 内容，只包含一个 Vue 应用的挂载点
  const popupHtml = `<div id="${MOUNT_POINT_ID}"></div>`;

  SillyTavern_API.callGenericPopup(popupHtml, SillyTavern_API.POPUP_TYPE.DISPLAY, BUTTON_TEXT, {
    wide: true,
    large: true,
    allowVerticalScrolling: true,
    buttons: [], // 没有按钮，关闭由弹窗右上角的'X'处理
    callback: function (action: string, popupJqueryObject: any) {
      console.log('[WIO] 弹窗已关闭:', action);
      // Vue 应用会被自动销毁，无需额外处理
    },
  });
  
  // 使用 setTimeout 确保弹窗的 DOM 已经添加到页面中
  setTimeout(() => {
    const parentDoc = window.parent.document;
    const mountPoint = parentDoc.getElementById(MOUNT_POINT_ID);
    if (mountPoint) {
      console.log('[WIO] 找到挂载点，正在初始化 Vue 应用...');
      initVueApp(mountPoint);
    } else {
      console.error(`[WIO] FATAL: 未能在弹窗中找到挂载点 #${MOUNT_POINT_ID}。`);
    }
  }, 100); // 100ms 延迟通常足够
}

/**
 * 将菜单项添加到 SillyTavern 的扩展菜单中
 */
function addMenuItem() {
  const parentDoc = window.parent.document;
  const extensionsMenu = jQuery_API('#extensionsMenu', parentDoc);
  if (!extensionsMenu.length) {
    console.error('[WIO] 扩展菜单 #extensionsMenu 未找到。');
    return;
  }

  // 避免重复添加
  if (jQuery_API(`#${MENU_ITEM_ID}`, extensionsMenu).length > 0) {
    console.log('[WIO] 菜单项已存在。');
    return;
  }
  
  // 采用与示例脚本一致的 HTML 结构
  const menuItemHTML = `
    <div class="list-group-item flex-container flexGap5 interactable" id="${MENU_ITEM_ID}" title="${BUTTON_TOOLTIP}">
      <div class="fa-fw fa-solid fa-book-open extensionsMenuExtensionButton"></div>
      <span>${BUTTON_TEXT}</span>
    </div>
  `;

  const $menuItem = jQuery_API(menuItemHTML);
  $menuItem.on('click', (event: { stopPropagation: () => void; }) => {
    event.stopPropagation();
    // 关闭扩展菜单本身
    const extensionsMenuButton = jQuery_API('#extensionsMenuButton', parentDoc);
    if (extensionsMenuButton.length && extensionsMenu.is(':visible')) {
        extensionsMenuButton.trigger('click');
    }
    openOptimizerPopup();
  });
  
  extensionsMenu.append($menuItem);
  console.log('[WIO] 菜单项已成功添加到扩展菜单。');
}

// --- 立即执行脚本 ---

// 假设父窗口的 API 在脚本加载时立即可用
// 这是一个基于用户反馈的权衡，移除了之前的安全等待机制
const parentWin = window.parent as any;

if (parentWin.SillyTavern && parentWin.TavernHelper && parentWin.jQuery) {
  console.log('[WIO] 立即加载模式: 检测到核心 API，正在初始化...');

  // 立即将 API 存储到全局变量中
  SillyTavern_API = parentWin.SillyTavern;
  TavernHelper_API = parentWin.TavernHelper;
  jQuery_API = parentWin.jQuery;
  
  // 立即将菜单项添加到 UI
  addMenuItem();

  console.log('[WIO] 脚本已立即初始化。');
} else {
  console.error('[WIO] FATAL: 立即加载失败，一个或多个 SillyTavern 核心 API (SillyTavern, TavernHelper, jQuery) 未找到。脚本无法执行。');
}