<template>
  <n-config-provider :theme="darkTheme">
    <n-global-style />
    <n-message-provider>
      <n-dialog-provider>
        <div class="wio-panel">
          <header class="wio-header">
            <h1>世界书管理器</h1>
            <!-- 刷新按钮等全局控件将放置于此 -->
          </header>
          <main class="wio-content">
            <!-- 使用 Vue Router 进行视图渲染 -->
            <router-view v-slot="{ Component }">
              <keep-alive>
                <component :is="Component" />
              </keep-alive>
            </router-view>
          </main>
        </div>
      </n-dialog-provider>
    </n-message-provider>
  </n-config-provider>
</template>

<script setup lang="ts">
import {
  NConfigProvider,
  NGlobalStyle,
  NMessageProvider,
  NDialogProvider,
  darkTheme,
} from 'naive-ui';

// 移除了对旧视图组件和 store 的直接导入，因为路由会处理视图的加载
</script>

<style scoped>
.wio-panel {
  display: flex;
  flex-direction: column;
  height: 100vh;
  padding: 16px;
  box-sizing: border-box;
}

.wio-header {
  margin-bottom: 16px;
}

.wio-content {
  flex-grow: 1;
  overflow-y: auto;
}
</style>