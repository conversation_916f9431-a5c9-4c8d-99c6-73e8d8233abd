# WIO 功能/状态/交互清单 (v4.0)

本文档是从现有代码库 (`ui.ts`, `events.ts`, `store.ts`, `elements.ts`, `views.ts`) 中逐行提取的功能、交互和状态依赖清单。它是 Vue 3 迁移项目验收的唯一金标准。

---

## 1. 核心功能点 (UI渲染)

### 1.1 世界书 (Lorebook)

-   [ ] **世界书分组 (`.wio-book-group`)**: 能够渲染一个完整的世界书折叠面板。
-   [ ] **折叠/展开按钮 (`.wio-collapse-toggle`)**:
    -   [ ] 能够正常渲染按钮。
    -   [ ] 按钮图标根据 `isCollapsed` 状态在 `▶` 和 `▼` 之间切换。
    -   [ ] `title` 属性根据 `isCollapsed` 状态在 '展开' 和 '折叠' 之间切换。
-   [ ] **全局启用开关 (`.wio-global-book-toggle`)**:
    -   [ ] 仅在 `isGlobal` 为 `true` 的视图中（即全局世界书标签页）渲染。
    -   [ ] `checked` 状态与 `book.enabled` 状态同步。
-   [ ] **书名与条目数**:
    -   [ ] 正确显示书名 `<h4>`。
    -   [ ] `书名` 能高亮匹配的搜索关键词。
    -   [ ] 正确显示条目总数 `(.wio-entry-count)`。
    -   [ ] 在搜索时，条目数能显示为 `匹配数 / 总数` 的格式。
-   [ ] **使用标识 (`.wio-usage-pill`)**:
    -   [ ] 当 `usage.length > 0` 时渲染。
    -   [ ] `title` 属性正确显示所有使用该书的角色列表。
-   [ ] **世界书控制按钮**:
    -   [ ] 渲染 "重命名" 按钮 (`.wio-rename-book-btn`)。
    -   [ ] 渲染 "删除" 按钮 (`.wio-delete-book-btn`)。
-   [ ] **条目列表 (`.wio-entry-list`)**:
    -   [ ] 当 `isCollapsed` 为 `false` 时渲染。
    -   [ ] 正确渲染其包含的所有条目 (`EntryItem`)。
    -   [ ] 渲染 "+ 新建条目" 按钮 (`.wio-create-entry-btn`)。

### 1.2 世界书条目 (Entry)

-   [ ] **条目容器 (`.wio-entry-item`)**: 能够渲染一个完整的条目行。
-   [ ] **多选框 (`.wio-multi-select-checkbox`)**:
    -   [ ] 仅在 `multiSelectMode` 为 `true` 时渲染。
    -   [ ] `checked` 状态与 `selectedItems` 集合中的状态同步。
-   [ ] **条目启用开关 (`.wio-entry-toggle`)**:
    -   [ ] `checked` 状态与 `entry.enabled` 状态同步。
-   [ ] **条目名称 (`.wio-entry-name`)**:
    -   [ ] 正确显示条目名称 (优先使用 `entry.comment`, 否则为 `(未命名条目)`)。
    -   [ ] 能高亮匹配的搜索关键词。
-   [ ] **关键词 (`.wio-entry-keys`)**:
    -   [ ] 正确显示以 `, ` 分隔的关键词列表。
    -   [ ] 能高亮匹配的搜索关键词。
-   [ ] **条目控制按钮**:
    -   [ ] 渲染 "编辑" 按钮 (`.wio-edit-entry-btn`)。
    -   [ ] 渲染 "删除" 按钮 (`.wio-delete-entry-btn`)。

### 1.3 正则表达式 (Regex)

-   [x] **正则项容器 (`.wio-regex-item`)**: 能够渲染一个完整的正则行。
-   [x] **拖拽功能**:
    -   [x] 当 `regex.source !== 'card'` 时，元素是可拖拽的 (`draggable="true"`)。
    -   [x] 当 `regex.source !== 'card'` 时，渲染拖拽图标 (`.wio-drag-handle`)。
-   [x] **正则启用开关 (`.wio-regex-toggle`)**:
    -   [x] `checked` 状态与 `regex.enabled` 状态同步。
-   [x] **正则名称 (`.wio-regex-name`)**:
    -   [x] 正确显示正则名称 (`script_name`)。
    -   [x] 能高亮匹配的搜索关键词。
-   [x] **来源标识 (`.wio-regex-source-badge`)**:
    -   [x] 当 `regex.source === 'card'` 时，渲染 "(卡)" 徽章。
-   [x] **查找与替换代码**:
    -   [x] 正确渲染查找表达式 (`.wio-regex-find`)。
    -   [x] 正确渲染替换表达式 (`.wio-regex-replace`)。
    -   [x] 查找与替换表达式都能高亮匹配的搜索关键词。
-   [x] **正则控制按钮**:
    -   [x] 渲染 "编辑" 按钮 (`.wio-edit-regex-btn`)。
    -   [x] 渲染 "删除" 按钮 (`.wio-delete-regex-btn`)。

### 1.4 视图 (Views)

-   [ ] **空状态提示 (`.wio-info-text`)**:
    -   [ ] 当没有世界书/正则时，显示相应的提示信息。
    -   [ ] 当搜索没有结果时，显示相应的提示信息。
-   [ ] **加载状态**:
    -   [ ] 当 `isLoading` 为 `true` 时，显示 "正在加载数据..."。
-   [ ] **错误状态**:
    -   [ ] 当 `loadError` 有值时，显示错误信息和 "重试" 按钮。
-   [ ] **未加载状态**:
    -   [ ] 当 `isDataLoaded` 为 `false` 时，显示 "点击刷新按钮加载数据"。

---

## 2. 交互行为

### 2.1 主面板

-   [ ] **打开面板**: 点击扩展菜单中的 "WIO" 按钮 (`#${BUTTON_ID}`)，会打开主面板。
-   [ ] **首次打开**: 首次打开面板时，会自动触发 `loadAllData`。
-   [ ] **关闭面板**: 点击面板右上角的 "×" (`.wio-close-btn`)，会关闭主面板。
-   [ ] **标签页切换**: 点击标签页按钮 (`.wio-tab-btn`) 会调用 `setActiveTab` 切换视图。

### 2.2 工具栏

-   [ ] **刷新数据**: 点击 "刷新数据" 按钮 (`#${REFRESH_BTN_ID}`) 或错误状态下的 "重试" 按钮，会调用 `loadAllData`。
-   [ ] **刷新角色数据**: 点击 "刷新角色数据" 按钮 (`#${REFRESH_CHARACTER_BTN_ID}`)，会调用 `refreshCharacterData`。
-   [ ] **新建世界书**: 点击 "新建世界书" 按钮 (`#${CREATE_LOREBOOK_BTN_ID}`)，会弹出输入框并调用 `createLorebook`。
-   [ ] **多选模式**: 点击 "多选模式" 按钮 (`#${MULTI_SELECT_BTN_ID}`)，会调用 `toggleMultiSelectMode`。
-   [ ] **全部折叠**: 点击 "全部折叠" 按钮 (`#wio-collapse-all-btn`)，会调用 `collapseAllBooks`。
-   [ ] **全部展开**: 点击 "全部展开" 按钮 (`#wio-expand-all-btn`)，会调用 `expandAllBooks`。
-   [ ] **折叠当前角色**: 点击 "折叠当前角色" 按钮 (`#${COLLAPSE_CURRENT_BTN_ID}`)，会调用 `collapseCurrentCharacterBooks`。

### 2.3 批量操作 (多选模式下)

-   [ ] **批量删除**: 点击 "批量删除" 按钮 (`#wio-bulk-delete-btn`)，会弹出确认框并调用 `performBulkDelete`。
-   [ ] **批量启用**: 点击 "批量启用" 按钮 (`#wio-bulk-enable-btn`)，会调用 `performBulkEnable`。
-   [ ] **批量禁用**: 点击 "批量禁用" 按钮 (`#wio-bulk-disable-btn`)，会调用 `performBulkDisable`。

### 2.4 批量操作 (非多选模式下)

-   [ ] **批量替换**: 点击 "批量替换" 按钮 (`#wio-bulk-replace-btn`)，会使用输入框内容弹出确认框并调用 `performBulkReplace`。

### 2.5 列表项交互

-   [ ] **折叠/展开世界书**: 点击 `.wio-collapse-toggle`，会调用 `toggleBookCollapse`。
-   [ ] **重命名世界书**: 点击 `.wio-rename-book-btn`，会弹出输入框并调用 `renameLorebook`。
-   [ ] **删除世界书**: 点击 `.wio-delete-book-btn`，会弹出确认框并调用 `deleteLorebook`。
-   [ ] **启用/禁用全局书**: 点击 `.wio-global-book-toggle` 复选框，会调用 `setGlobalLorebookEnabled`。
-   [ ] **新建条目**: 点击 `.wio-create-entry-btn`，会弹出条目编辑器并调用 `createLorebookEntry`。
-   [ ] **编辑条目**: 点击 `.wio-edit-entry-btn`，会弹出条目编辑器并调用 `updateLorebookEntry`。
-   [ ] **删除条目**: 点击 `.wio-delete-entry-btn`，会弹出确认框并调用 `deleteLorebookEntry`。
-   [ ] **启用/禁用条目**: 点击 `.wio-entry-toggle` 复选框，会调用 `updateLorebookEntry`。
-   [x] **新建正则**: 点击 `.wio-create-regex-btn`，会弹出正则编辑器并调用 `createRegex`。
-   [x] **编辑正则**:
    -   [x] 点击 `.wio-edit-regex-btn`，会弹出正则编辑器并调用 `updateRegex`。
    -   [x] 如果正是卡片内正则 (`source === 'card'`)，点击编辑时会弹出提示，不允许编辑。
-   [x] **删除正则**:
    -   [x] 点击 `.wio-delete-regex-btn`，会弹出确认框并调用 `deleteRegex`。
    -   [x] 如果正是卡片内正则，点击删除时会弹出提示，不允许删除。
-   [x] **启用/禁用正则**: 点击 `.wio-regex-toggle` 复选框，会调用 `updateRegex`。
-   [ ] **搜索**: 在搜索框 (`#${SEARCH_INPUT_ID}`) 中输入文本，会调用 `setSearchQuery` 并实时过滤列表。

---

## 3. 状态驱动的UI变化

-   [ ] **工具栏显隐**:
    -   [ ] **批量搜索/替换**: 仅在 `isLorebookTab` 且 `!multiSelectMode` 时显示。
    -   [ ] **多选按钮**: 仅在 `isLorebookTab` 时显示。
    -   [ ] **多选模式按钮高亮**: 当 `multiSelectMode` 为 `true` 时，多选按钮具有 `.active` 类。
    -   [ ] **批量操作按钮 (删/启/禁)**: 仅在 `multiSelectMode` 和 `isLorebookTab` 为 `true` 时显示。
    -   [ ] **折叠/展开按钮**: 仅在 `isLorebookTab` 时显示。
    -   [ ] **折叠当前角色按钮**: 仅在 `activeTab` 为 `char-lore` 或 `chat-lore` 时显示。
    -   [ ] **刷新角色按钮**: 仅在 `isCharacterTab` (角色/聊天世界书，角色正则) 时显示。
-   [ ] **按钮禁用状态**:
    -   [ ] **批量操作按钮 (删/启/禁)**: 当 `selectedItems.size === 0` 时，按钮为 `disabled` 状态。
-   [ ] **切换标签页重置状态**:
    -   [ ] 调用 `setActiveTab` 切换标签页时，`multiSelectMode` 必须重置为 `false`，且 `selectedItems` 必须被清空。