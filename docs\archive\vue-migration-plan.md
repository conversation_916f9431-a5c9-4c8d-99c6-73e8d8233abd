
# WIO插件UI迁移至Vue 3技术方案 (v4.0 "零信任" 质量保证版)

**文档版本**: 4.0
**设计者**: Test Architect & Quality Advisor

### 概要

本文档为 **World Info Optimizer (WIO)** 插件UI迁移提供了一套以**代码考古学**和**多层质量保证**为核心的顶级工程化方案。此方案v4.0版本的核心是**“零信任”原则**：我们不依赖记忆或文档来定义功能，而是直接从现有代码库中提取每一个功能点、状态依赖和UI行为，以此作为迁移的金标准，确保**100%的功能覆盖率和零回归**。

---

### 1. 核心理念：零信任迁移

*   **唯一真相来源 (Single Source of Truth)**: 现有的 `ui.ts`, `ui/` 目录下的代码，以及 `store.ts`, `events.ts` 是定义当前功能的唯一真相。所有迁移任务都必须追溯到这些文件。
*   **清单驱动开发 (Checklist-Driven Development)**: 在迁移开始前，必须完成一份从代码中逐行提取的、详尽的**功能/状态/交互清单**。此清单是验收的唯一依据。
*   **分层测试策略 (Layered Testing Strategy)**: 我们将结合单元测试、组件行为测试、视觉回归测试和端到端（E2E）测试，为迁移过程提供全方位的质量防护网。
*   **绞杀榕模式 (Strangler Fig Pattern)**: 继续沿用v3.0方案中提到的安全模式，确保新旧系统平滑过渡。

---

### 2. 技术选型与质量保证生态

#### 2.1 UI 框架最终选型 (基于“废弃旧设计”的新视角)

**核心决策转变**: 鉴于您明确表示“不需要保留自己的CSS变量和设计”，我们得以彻底解放技术选型，从“如何精确复刻”转向“如何构建最佳体验”。这意味着我们可以**全面拥抱一个现代化的、设计完善的UI框架**，从而极大地提升开发效率、UI质量和长期可维护性。

**最终推荐：Naive UI**

| 对比维度       | Naive UI                                                                                       | Element Plus / Ant Design Vue                      | Headless UI + Tailwind         |
| :------------- | :--------------------------------------------------------------------------------------------- | :------------------------------------------------- | :----------------------------- |
| **设计哲学**   | 灵活、主题化、性能优先                                                                         | 企业级、规范化                                     | 完全无样式，自由度最高         |
| **TS支持**     | **顶级支持**。所有组件和API都用TypeScript编写，提供完美的类型推断。                            | 良好，但有时类型定义较为复杂。                     | 需要为自定义组件编写所有类型。 |
| **组件丰富度** | **非常丰富**，涵盖了本项目需要的所有组件（按钮、输入框、模态框、列表、标签页等），且质量很高。 | 丰富，但设计风格较为固定。                         | 从零开始构建，或依赖社区。     |
| **主题定制**   | **极其强大**。可通过JS对象覆盖所有设计变量，轻松实现暗色模式或与宿主环境风格匹配的主题。       | 相对困难，通常需要覆盖大量CSS变量或使用Sass/Less。 | -                              |
| **开发效率**   | **最高**。直接使用高质量组件，无需关心底层实现和样式。                                         | 高，但定制时效率下降。                             | 最低，需要手动实现所有样式。   |
| **最终体积**   | 按需引入，体积可控且合理。                                                                     | 按需引入，但单组件体积较大。                       | 最小，但开发成本最高。         |

**选择 Naive UI 的核心理由**:

1.  **开发效率最大化**: 我们可以直接利用 Naive UI 提供的大量高质量组件，将开发重心完全放在业务逻辑上，而不是花费大量时间去“造轮子”和调试CSS。
2.  **一流的TypeScript体验**: Naive UI 的作者本身就是Vue核心团队成员，其对TypeScript的深刻理解融入到了框架的每一个角落，这将极大提升我们代码的健壮性和可维护性。
3.  **灵活且强大的主题系统**: 即使我们放弃了旧设计，Naive UI 的主题系统也能让我们轻松地微调UI风格，使其在未来能更好地融入SillyTavern的视觉环境中，实现一种新的、更专业的“和谐统一”。
4.  **性能出色**: Naive UI 在设计之初就非常注重性能，这对作为一个需要流畅运行的浏览器插件至关重要。

#### 2.2 更新后的UI构建策略：全面拥抱 Naive UI

**核心策略：将 Naive UI 作为项目的UI构建基石，废弃所有自定义的CSS组件样式，仅保留必要的布局和覆盖样式。**

1.  **全面采用 Naive UI 组件**: 项目中所有基础UI元素（按钮、输入框、复选框、模态框等）都将使用 Naive UI 对应的组件进行重构。
2.  **配置全局主题**: 我们将在 `App.vue` 的根节点配置 `n-config-provider`，定义应用的基础主题（如暗色模式），并可以按需覆盖特定组件的样式变量。
3.  **简化CSS架构**: 原计划中的 “CSS架构审计” 将被大幅简化。我们不再需要迁移 `wio-` 前缀的组件样式。CSS文件将主要用于：
    *   定义应用级的布局（如 `WioPanel.vue` 的flex布局）。
    *   覆盖 Naive UI 中极少数不符合我们需求的特定样式。
    *   定义一些非组件的全局辅助类。
4.  **移除 `SweetAlert2` 依赖**: Naive UI 自带了功能强大的 `Dialog` 和 `Message` API，可以完全取代当前对 `parent.Swal` 的依赖，使插件更加独立和稳定。

#### 2.3 最终技术栈与质量保证生态 (决定版)

| 类别              | 技术/工具                              | 质量保证目标                                                                                 |
| :---------------- | :------------------------------------- | :------------------------------------------------------------------------------------------- |
| **核心框架**      | `Vue 3`, `Vite`, `TypeScript`, `Pinia` | 提供强大的类型安全和开发效率。                                                               |
| **UI构建**        | **Naive UI**                           | **提供高质量、一致性、可维护性强的UI，并极大提升开发效率。**                                 |
| **单元/组件测试** | `Vitest` + `Vue Testing Library`       | 验证业务逻辑和组件集成是否正确。由于UI由框架保证，测试重点将从视觉转向功能。                 |
| **视觉回归测试**  | `Storybook`                            | **可选**。由于采用标准化组件库，视觉回归的重要性降低，但仍可用于展示复杂业务组件的不同状态。 |
| **E2E测试**       | `Playwright`                           | 模拟真实用户操作，验证跨组件的复杂交互流程是否正常。                                         |
| **代码规范**      | `ESLint` + `Prettier`                  | 静态代码分析，预防潜在错误。                                                                 |

---

### 3. 终极迁移执行计划

#### **阶段 0: 代码考古学与清单化 (The Code Autopsy)**

**目标**: 将隐式的代码逻辑转化为显式的、可跟踪的迁移需求。

1.  **功能点提取 (Functionality Extraction)**:
    *   **输入**: `elements.ts`, `views.ts`
    *   **任务**: 逐个分析 `create...` 和 `render...` 函数，将每一个动态生成的HTML元素（按钮、复选框、文本）及其 `data-*` 属性，都记录到功能清单 `docs/wio-functionality-checklist.md` 中。
    *   **示例**:
        ```markdown
        - [ ] **Book Header**: 渲染折叠按钮 `wio-collapse-toggle`
        - [ ] **Book Header**: `wio-collapse-toggle` 的 `title` 属性根据 `isCollapsed` 状态动态变为 '展开' 或 '折叠'
        - [ ] **Book Header**: 当 `isGlobal` 为 `true` 时，渲染全局启用复选框 `wio-global-book-toggle`
        ```

2.  **交互行为提取 (Interaction Extraction)**:
    *   **输入**: `ui.ts` (jQuery事件绑定部分), `events.ts`
    *   **任务**: 查找所有 `.on('click', ...)` 或类似的事件监听器。将选择器、事件类型和回调函数的核心逻辑翻译成用户行为，记录到清单中。
    *   **示例**:
        ```markdown
        - [ ] **交互**: 点击 `.wio-rename-book-btn` 会触发重命名书本的模态框
        - [ ] **交互**: 在 `#wio-search-input` 中输入文本，会触发 `render()` 并过滤列表
        ```

3.  **状态与逻辑依赖分析 (State & Logic Analysis)**:
    *   **输入**: `store.ts`, `ui.ts` (特别是 `updateToolbarButtons` 函数)
    *   **任务**: 分析并绘制状态依赖图。明确哪些UI元素的显隐/禁用状态依赖于一个或多个应用状态。
    *   **产出**: 在清单中记录状态驱动的UI变化。
    *   **示例**:
        ```markdown
        - [ ] **状态驱动UI**: `#wio-bulk-delete-btn` 按钮的显示条件是 `state.multiSelectMode === true && isLorebookTab === true`
        - [ ] **状态驱动UI**: `#wio-bulk-delete-btn` 按钮的 `disabled` 属性依赖于 `state.selectedItems.size > 0`
        ```

4.  **CSS架构审计 (CSS Architecture Audit)**:
    *   **输入**: `ui.ts` (styles变量), `ui-style-guide.md`
    *   **任务**: 将巨大的样式字符串分类：
        *   **A. 设计令牌 (Design Tokens)**: 所有CSS变量 (`--wio-...`)。
        *   **B. 全局样式 (Global Styles)**: 如 `body`, `*`, 滚动条样式，媒体查询等。
        *   **C. 组件样式 (Component Styles)**: 所有带 `wio-` 前缀的类。
        *   **D. 外部库覆盖 (Overrides)**: 所有 `.swal2-...` 的样式。
    *   **产出**: 明确的CSS迁移策略：A -> `assets/styles/design-tokens.css`, B -> `App.vue` 的非scoped `<style>`, C -> 各自Vue组件的 `<style scoped>`, D -> 一个独立的 `assets/styles/overrides.css` 或在 `App.vue` 中处理。

#### **阶段 1: 环境、桥接与技术验证**

**目标**: 搭建一个可测试、可验证的现代化开发环境。

1.  **环境搭建**: 同v3.0，搭建Vite项目并集成所有DX工具。
2.  **实现BridgeAPI契约**:
    *   **输入**: `api.ts`, `core.ts`, `modals.ts`, `notifications.ts`
    *   **任务**: 基于现有代码的函数签名，在 `bridge/types.ts` 中创建**精确的**接口定义。这不再是猜测，而是直接翻译。
    *   **示例 (`bridge/types.ts`)**:
        ```typescript
        import { EntryEditorOptions, LorebookEntry } from '../types';

        export interface BridgeAPI {
          // 来自 modals.ts
          showEntryEditorModal(options: EntryEditorOptions): Promise<LorebookEntry>;
          // 来自 core.ts (假设)
          deleteLorebook(bookName: string): Promise<void>;
          // 其它API...
        }
        ```
3.  **PoC与Storybook集成**:
    *   **任务**: 创建一个 `BaseButton.vue` 组件。在Storybook中为其编写一个故事（story）。运行`test-storybook`命令，**验证视觉回归测试流程可以正常工作**。
    *   **意义**: 提前验证我们最重要的质量保证机制之一。

#### **阶段 2 -> 3: 清单驱动的增量绞杀**

**目标**: 逐项划掉功能清单，安全地用Vue实现替换旧功能。

1.  **选择一个功能点**: 从 `wio-functionality-checklist.md` 中选择一个未完成的项，例如：“渲染 `wio-regex-item`”。
2.  **TDD式组件开发 (Test-Driven Development)**:
    *   **写测试**: 先为 `RegexItem.vue` 组件编写一个失败的Vitest测试，描述其应有的行为（例如：“当传入的正则source为'card'时，应显示'(卡)'徽章”）。
    *   **写代码**: 编写最少的组件代码使测试通过。
    *   **Storybook开发**: 在Storybook中调试组件的各种状态（启用、禁用、可拖拽等）。
    *   **视觉快照**: 运行Storybook测试，生成组件的视觉快照作为基准。
3.  **实现交互**: 在组件中添加交互逻辑，通过 `useBridge()` 调用宿主API。
4.  **集成与验证**: 将组件集成到视图中，手动测试该功能点。
5.  **更新清单**: 测试通过后，在清单中勾选该项：`- [x] ...`。
6.  **循环**: 重复以上步骤，直到清单中的所有项目都被勾选。

#### **阶段 4: 废弃与验证**

**目标**: 在清单100%完成后，安全地移除旧代码并进行最终验证。

1.  **代码清理**: 同v3.0，安全地移除旧的UI相关文件和依赖。
2.  **最终验收**:
    *   **功能清单复核**: 再次确认 `wio-functionality-checklist.md` 中所有项都已勾选。
    *   **E2E测试 (如果实施)**: 运行完整的E2E测试套件。
    *   **交叉测试**: 邀请另一位不熟悉迁移过程的开发者，根据旧版UI的行为，对新版UI进行探索性测试，以发现任何预期之外的行为差异。

---

### 4. Vue 应用架构设计

为了确保迁移过程的结构化和可预见性，我们预先定义清晰的应用架构。

#### 4.1 组件化拆分策略 (Component Breakdown)

我们将遵循“原子设计”思想，将现有UI逻辑拆分为层次分明、高内聚、低耦合的Vue组件。

*   **根组件 (Root Component)**
    *   `App.vue`: 应用主入口，负责挂载不同视图，管理全局样式和覆盖层。
*   **布局组件 (Layout Components)**
    *   `src/components/WioPanel.vue`: 整体面板框架，包含Header, Tabs, Toolbar, Main Content, Footer。
    *   `src/components/WioHeader.vue`: 顶部标题栏。
    *   `src/components/WioTabs.vue`: 选项卡导航。
    *   `src/components/WioToolbar.vue`: 工具栏，包含搜索框和各种功能按钮。
*   **视图组件 (View Components)**
    *   `src/views/GlobalLorebookView.vue`: 全局世界书视图。
    *   `src/views/CharacterLorebookView.vue`: 角色世界书视图。
    *   `src/views/ChatLorebookView.vue`: 聊天世界书视图。
    *   `src/views/RegexView.vue`: 可复用的正则列表视图 (用于全局和角色)。
*   **原子/业务组件 (Atomic/Business Components)**
    *   `src/components/LorebookGroup.vue`: 单个世界书的折叠面板。
    *   `src/components/EntryItem.vue`: 单个世界书条目。
    *   `src/components/RegexItem.vue`: 单个正则条目。
    *   `src/components/BaseButton.vue`: 基础按钮封装 (用于演示Storybook)。
    *   `src/components/InfoText.vue`: “没有找到...”之类的提示信息组件。

#### 4.2 状态管理：Pinia Store 设计

`store.ts` 中的逻辑将被精确地映射到一个或多个Pinia store模块中。核心的 `appStore` 定义如下：

**`src/stores/app.ts`**
```typescript
import { defineStore } from 'pinia';
// ... 导入类型

export const useAppStore = defineStore('app', {
  state: () => ({
    // UI状态
    activeTab: 'global-lore',
    searchQuery: '',
    isLoading: false,
    loadError: null as string | null,
    isDataLoaded: false,
    collapsedBooks: new Set<string>(),

    // 多选模式相关
    multiSelectMode: false,
    selectedItems: new Set<string>(),

    // 核心数据
    allLorebooks: [] as LorebookFile[],
    lorebookEntries: new Map<string, LorebookEntry[]>(),
    lorebookUsage: new Map<string, string[]>(),
    regexes: { global: [], character: [] } as { global: TavernRegex[], character: TavernRegex[] },
    
    // ... 其他从 store.ts 提取的状态
  }),

  getters: {
    // 示例 Getter
    isLorebookTab: (state): boolean =>
      ['global-lore', 'char-lore', 'chat-lore'].includes(state.activeTab),
    
    hasSelectedItems: (state): boolean => state.selectedItems.size > 0,
  },

  actions: {
    // 示例 Action
    async fetchData() {
      this.isLoading = true;
      try {
        // ... 调用 bridgeAPI 获取数据
        // this.allLorebooks = ...
        this.isDataLoaded = true;
        this.loadError = null;
      } catch (error) {
        this.loadError = (error as Error).message;
      } finally {
        this.isLoading = false;
      }
    },
    
    setActiveTab(tabId: string) {
      this.activeTab = tabId;
      // 切换Tab时重置多选状态
      this.multiSelectMode = false;
      this.selectedItems.clear();
    },

    toggleBookCollapse(bookName: string) {
      if (this.collapsedBooks.has(bookName)) {
        this.collapsedBooks.delete(bookName);
      } else {
        this.collapsedBooks.add(bookName);
      }
    },
    // ... 其他所有 actions，如 toggleMultiSelect, deleteSelectedItems 等
  },
});
```
这种结构将使状态变更**可预测、可调试**，并与组件逻辑完全解耦。

---

### 5. 风险分析与缓解策略

主动识别并管理风险是确保项目成功的关键。

| 风险类别     | 具体风险描述                                                                                       | 可能性 | 影响 | 缓解策略                                                                                                                                                                                                                                                                  |
| :----------- | :------------------------------------------------------------------------------------------------- | :----- | :--- | :------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------ |
| **技术风险** | **样式冲突**: 新的Vue组件样式意外泄露，或被SillyTavern的全局样式污染。                             | 中     | 高   | 1.  **强制使用 `<style scoped>`**: 确保所有组件样式都封装在组件内部。 <br> 2. **BEM命名法**: 即使在scoped内，也使用如 `wio-book-group__header` 的规范命名，便于调试。 <br> 3. **CSS Reset**: 在 `App.vue` 的非scoped样式中引入一个轻量级的CSS Reset，以统一基础元素样式。 |
| **技术风险** | **性能瓶颈**: 当世界书条目数量巨大时 (上千条)，UI渲染可能出现卡顿。                                | 低     | 中   | 1.  **虚拟滚动 (Virtual Scrolling)**: 对 `wio-entry-list` 和 `wio-regex-list` 的渲染，预留引入 `vue-virtual-scroller` 或类似库的接口。初期可不实现，但架构上要支持。 <br> 2. **性能分析**: 使用 Vue DevTools 性能分析器定期检查组件渲染成本。                             |
| **集成风险** | **宿主环境API变更**: SillyTavern更新，导致 `parent.Swal` 或 `parent.jQuery` 等依赖失效或行为变更。 | 中     | 高   | **`BridgeAPI` 作为防腐层 (Anti-Corruption Layer)**: 所有对宿主环境的调用都必须通过 `BridgeAPI`。这使得外部变化只影响 `Bridge` 的实现，而Vue应用内部代码无需改动。同时在 `Bridge` 中增加对API存在的检查和优雅降级处理。                                                    |
| **流程风险** | **功能点遗漏**: 在“代码考古”阶段未能完整识别出所有的UI行为和边界条件。                             | 低     | 高   | 1.  **交叉验证**: 清单完成后，由另一名不参与提取的开发者，对照运行中的旧版插件进行复核。 <                                                                                                                                                                                |