# UX 设计与交互规范 - Worldbook 管理器

**版本**: 1.0
**状态**: 草稿

---

## 1. 概述

本文档旨在为 "Worldbook 管理器" 项目提供一套完整、统一的用户体验 (UX) 设计原则和交互规范。它是连接 [`PRD.md`](./PRD.md) (用户需求) 和具体 UI 实现的桥梁，将指导开发团队如何使用 [`TechSpec.md`](./TechSpec.md) 中定义的技术栈（特别是 Naive UI）来构建一个直观、高效且用户友好的界面。

所有团队成员（PO, Architect, Developer, QA）都应遵循此规范，以确保最终产品在交互和视觉上的一致性。

---

## 相关文档

*   **[产品需求文档 (PRD)](./PRD.md)**: 本文档中所有用户流程和信息架构的需求来源。
*   **[技术规格书 (Tech Spec)](./TechSpec.md)**: 本文档所依据的技术选型和架构。
*   **[项目路线图 (Roadmap)](./Roadmap.md)**: 实现这些UX设计的具体开发计划。

---

## 2. UX 核心原则

我们的设计决策将遵循以下核心原则：

*   **清晰性 (Clarity)**: 界面信息一目了然，用户始终清楚自己在哪，能做什么。
*   **效率 (Efficiency)**: 简化高频操作路径，提供批量处理等效率工具，尊重用户的时间。
*   **及时反馈 (Responsiveness)**: 用户的任何操作都应立即获得视觉或状态上的反馈。系统状态（如加载、成功、失败）必须清晰可见。
*   **一致性 (Consistency)**: 相同的操作应使用相同的控件和交互模式。遵循既定的组件应用规范。
*   **容错性 (Forgiveness)**: 对于破坏性操作（如删除），必须提供二次确认，尽可能让操作可逆或可预防。

---

## 3. 信息架构 (Information Architecture)

应用的核心信息结构遵循“列表 -> 详情”的两级模式。

### 3.1 列表页 (Worldbook List View)

*   **核心目的**: 提供所有可用 Worldbook 的概览，并作为管理和导航的入口。
*   **承载信息**:
    *   Worldbook 名称
    *   条目 (Entry) 数量
    *   启用/禁用状态
    *   (可选) 被哪些角色使用
*   **主要操作**:
    *   导航至详情页 (主要)
    *   新建 Worldbook
    *   重命名 Worldbook
    *   删除 Worldbook
    *   切换启用/禁用状态

### 3.2 详情页 (Worldbook Detail View)

*   **核心目的**: 深入管理单个 Worldbook 内的所有条目 (Entries)。
*   **承载信息**:
    *   当前 Worldbook 名称 (作为标题)
    *   条目列表
        *   条目名称 / 关键词
        *   启用/禁用状态
        *   折叠/展开状态
*   **主要操作**:
    *   返回列表页
    *   新建条目
    *   编辑条目
    *   删除条目
    *   切换条目启用/禁用状态
    *   折叠/展开条目内容
    *   进入/退出多选模式
    *   批量操作 (基于多选)
    *   搜索与替换

---

## 4. 关键用户流程 (Key User Flows)

以下是用户完成核心任务的典型路径，所有界面设计应以简化这些流程为目标。

### 4.1 核心浏览与编辑流程

1.  **进入应用**: 用户看到 **[列表页]**，显示 Worldbook 列表的加载状态 (`n-spin`)。
2.  **数据加载完成**: 列表显示所有 Worldbook。
3.  **选择 Worldbook**: 用户点击列表中的 "My Awesome Worldbook"。
4.  **导航至详情**: 应用切换到 **[详情页]**，URL发生变化。
5.  **查看条目**: 详情页显示 "My Awesome Worldbook" 的所有条目。
6.  **点击编辑**: 用户点击条目 "Entry A" 旁的编辑按钮。
7.  **打开编辑器**: 弹出一个模态框 (`n-modal`)，其中包含 "Entry A" 的内容编辑器。
8.  **修改并保存**: 用户修改内容后点击“保存”。
9.  **成功反馈**: 模态框关闭，应用显示一个全局成功提示 (`n-message`)，详情页中的 "Entry A" 数据更新。

### 4.2 高级批量操作流程

1.  **进入多选模式**: 在 **[详情页]**，用户点击“多选”按钮。
2.  **界面变化**: 界面进入多选状态，每个条目前出现复选框，批量操作按钮（删除、启用、禁用）变为可用状态。
3.  **选择条目**: 用户勾选 "Entry B", "Entry C", "Entry D"。
4.  **执行批量删除**: 用户点击“批量删除”按钮。
5.  **操作确认**: 应用弹出确认对话框 (`n-dialog`)，提示“您确定要删除选中的 3 个条目吗？”
6.  **确认操作**: 用户点击“确定”。
7.  **成功反馈**: 对话框关闭，应用显示全局成功提示 (`n-message`)，被选中的三个条目从列表中移除。
8.  **退出多选**: 用户再次点击“多选”按钮，界面返回正常浏览模式。

---

## 5. 组件应用规范 (Component Usage Guide)

为保证体验一致性，所有交互应遵循以下基于 **Naive UI** 的规范：

*   **破坏性操作确认**: **必须** 使用 `Dialog` (对话框) 组件进行二次确认。
    *   **场景**: 删除 Worldbook, 删除条目, 批量删除。
    *   **文案**: 必须清晰说明操作的后果，例如：“您确定要删除 'My Awesome Worldbook' 吗？此操作无法撤销。”
*   **操作成功/失败反馈**: **应** 使用 `Message` (全局消息) 组件进行轻量、非阻塞的反馈。
    *   **场景**: 保存成功, 重命名成功, 启用/禁用状态切换。
    *   **样式**: 成功使用 `success` 类型，失败使用 `error` 类型。
*   **信息补充与解释**: **应** 使用 `Tooltip` (文字提示) 或 `Popover` (弹出卡片) 来解释图标按钮或提供额外信息。
    *   **场景**: 图标按钮 (如编辑、删除), 复杂的术语解释。

---

## 6. 核心界面线框图 (Text-based)

以下是核心视图的低保真布局草图，用于沟通信息层级和功能排布。

### 6.1 列表页 (Worldbook List View)

```
+-------------------------------------------------------------------+
| [标签页: 全局 | 角色 | 聊天]                            [刷新 ICON] |
+-------------------------------------------------------------------+
| [搜索框: 搜索Worldbook...]                  [新建Worldbook BTN] |
+-------------------------------------------------------------------+
|                                                                   |
| +-- Worldbook A ------------------ [条目: 15] -- [启用 SW] [>] --+ |
| +-- Worldbook B ------------------ [条目: 8] --- [启用 SW] [>] --+ |
| +-- Worldbook C (被角色使用) ----- [条目: 23] -- [启用 SW] [>] --+ |
|                                                                   |
|                  [加载中... / 暂无数据 / 加载失败]                   |
|                                                                   |
+-------------------------------------------------------------------+
```

### 6.2 详情页 (Worldbook Detail View)

```
+-------------------------------------------------------------------+
| [ < 返回列表] [Worldbook A]                        [刷新 ICON]     |
+-------------------------------------------------------------------+
| [搜索框: 搜索条目...] [多选 BTN] [批量操作 BTN] [新建条目 BTN] |
+-------------------------------------------------------------------+
|                                                                   |
| +-- [v] Entry 1 -- [关键字...] -- [启用 SW] [编辑 ICON] [删除 ICON] --+ |
| |   Entry 1 的详细内容...                                         |
| +-------------------------------------------------------------------+
| +-- [>] Entry 2 -- [关键字...] -- [启用 SW] [编辑 ICON] [删除 ICON] --+ |
| +-- [>] Entry 3 -- [关键字...] -- [启用 SW] [编辑 ICON] [删除 ICON] --+ |
|                                                                   |
|                  [加载中... / 暂无数据 / 加载失败]                   |
|                                                                   |
+-------------------------------------------------------------------+
```

---

## 7. 界面状态设计 (UI States Design)

应用在任何时候都必须向用户清晰地传达其当前状态。

*   **加载中 (Loading State)**:
    *   **全页加载**: 进入应用或刷新等重量级操作，应使用 `n-spin` 组件覆盖整个内容区域，并显示清晰的加载文案（如“数据加载中...”）。
    *   **局部加载**: 单个操作（如保存条目），可以在触发按钮上显示加载状态。
*   **空状态 (Empty State)**:
    *   **场景**: Worldbook 列表为空, 详情页中没有条目, 搜索结果为空。
    *   **表现**: 必须使用 `n-empty` 组件，并提供清晰的提示文案和下一步操作指引。例如：“暂无 Worldbook，点击‘新建’来创建第一个吧！”
*   **错误状态 (Error State)**:
    *   **场景**: API 请求失败，数据加载错误。
    *   **表现**: 必须在界面上显示错误信息，并提供一个明确的“重试”按钮。可以使用 `n-result` 组件来格式化展示。
