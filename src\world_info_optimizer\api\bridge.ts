// SillyTavern.v2WorldInfoBook 和 SillyTavern.v2DataWorldInfoEntry 的类型定义
// 通常这些类型会从一个全局的 .d.ts 文件中导入
export interface WorldbookEntry {
  keys: string[];
  content: string;
  enabled: boolean;
  comment: string;
  secondary_keys: string[];
  constant: boolean;
  selective: boolean;
  insertion_order: number;
  position: 'before_char' | 'after_char';
  id: number;
  extensions: object;
}

export interface Worldbook {
  name: string;
  entries: WorldbookEntry[];
}

export interface WorldbookListItem {
  name: string;
  entryCount: number;
}

/**
 * 定义了应用与 SillyTavern 宿主环境通信的唯一接口契约。
 * 这是应用的防腐层 (Anti-Corruption Layer)。
 */
export interface IBridgeAPI {
  /**
   * 异步加载指定名称的 Worldbook。
   * @param name - Worldbook 的名称。
   * @returns 返回一个 Promise，解析为 Worldbook 对象；如果失败则为 null。
   */
  loadWorldbook(name: string): Promise<Worldbook | null>;

  /**
   * 异步保存 Worldbook。
   * @param name - Worldbook 的名称。
   * @param data - 要保存的 Worldbook 对象。
   * @returns 返回一个 Promise，在保存完成后解析。
   */
  saveWorldbook(name: string, data: Worldbook): Promise<void>;

  /**
   * 高效地异步获取所有 Worldbook 的元数据列表。
   * @returns 返回一个 Promise，解析为一个包含 WorldbookListItem 的数组。
   */
  getWorldbookList(): Promise<WorldbookListItem[]>;

  /**
   * 注册一个回调函数，当宿主环境中的 Worldbook 数据更新时触发。
   * @param callback - 当事件发生时调用的函数。
   * @returns 返回一个用于取消订阅的函数。
   */
  onWorldbookUpdated(callback: (name: string, data: { entries: WorldbookEntry[] }) => void): () => void;
}

/**
 * BridgeAPI 的模拟实现，用于开发和测试。
 * 它返回静态数据，模拟与真实宿主环境的交互。
 */
class MockBridgeAPI implements IBridgeAPI {
  private mockWorldbooks: Map<string, Worldbook> = new Map();

  constructor(simulateErrors = false) {
    this.initializeData();
    if (simulateErrors) {
      this.setupErrorSimulations();
    }
  }

  private initializeData() {
     // 初始化一些模拟数据
     this.mockWorldbooks.set('My Awesome Worldbook', {
      name: 'My Awesome Worldbook',
      entries: [
        { id: 1, keys: ['aragorn'], content: 'The heir of Isildur.', enabled: true, comment: '', secondary_keys: [], constant: false, selective: false, insertion_order: 10, position: 'before_char', extensions: {} },
        { id: 2, keys: ['gondor'], content: 'A kingdom of men.', enabled: true, comment: '', secondary_keys: [], constant: false, selective: false, insertion_order: 10, position: 'before_char', extensions: {} },
      ],
    });
    this.mockWorldbooks.set('Magic Spells', {
        name: 'Magic Spells',
        entries: [
          { id: 3, keys: ['fireball'], content: 'A fiery explosion.', enabled: true, comment: '', secondary_keys: [], constant: false, selective: false, insertion_order: 10, position: 'before_char', extensions: {} },
        ],
    });
    this.mockWorldbooks.set('Empty Book', {
        name: 'Empty Book',
        entries: [],
    });
  }

  private setupErrorSimulations() {
    // 模拟一个加载会失败的书
    this.mockWorldbooks.set('Book of Errors', {
        name: 'Book of Errors',
        entries: [],
    });
  }


  async loadWorldbook(name: string): Promise<Worldbook | null> {
    console.log(`[MockBridge] Loading worldbook: ${name}`);
    await this.simulateDelay();

    if (name === 'Book of Errors') {
        throw new Error('Permission denied to read this cursed book.');
    }
    
    return this.mockWorldbooks.get(name) || null;
  }

  async saveWorldbook(name: string, data: Worldbook): Promise<void> {
    console.log(`[MockBridge] Saving worldbook: ${name}`, data);
    await this.simulateDelay();
    this.mockWorldbooks.set(name, data);
  }

  async getWorldbookList(): Promise<WorldbookListItem[]> {
    console.log('[MockBridge] Getting worldbook list...');
    await this.simulateDelay();
    return Array.from(this.mockWorldbooks.values()).map(book => ({
      name: book.name,
      entryCount: book.entries.length,
    }));
  }

  onWorldbookUpdated(callback: (name: string, data: { entries: WorldbookEntry[] }) => void): () => void {
    console.log('[MockBridge] Registered listener for worldbook updates.');
    const handler = (event: any) => {
        // 在真实的适配器中，这里会监听 `tavern_events.WORLDINFO_UPDATED`
        // 这里我们只做日志记录
        console.log('Simulated worldbook update event received:', event);
    };
    
    // 返回一个取消订阅的函数
    return () => {
      console.log('[MockBridge] Unregistered listener for worldbook updates.');
    };
  }

  private simulateDelay(ms: number = 500): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }
}

/**
 * 工厂函数，用于创建 BridgeAPI 的实例。
 * 这使得我们可以根据环境（开发、测试、生产）选择不同的实现。
 * @param type - 'mock' | 'real'
 * @returns IBridgeAPI 的一个实例
 */
export function createBridge(type: 'mock' | 'real' = 'mock'): IBridgeAPI {
    switch (type) {
        case 'mock':
            return new MockBridgeAPI();
        // 在未来的开发中，这里将添加 'real' case 来返回与真实 SillyTavern 环境通信的适配器。
        // case 'real':
        //     return new RealBridgeAPI();
        default:
            console.warn(`Unknown bridge type: ${type}. Falling back to mock.`);
            return new MockBridgeAPI();
    }
}
