<template>
  <div class="worldbook-detail-view">
    <n-page-header @back="router.back">
      <template #title>
        <h2>{{ name }}</h2>
      </template>
      <!-- 右侧操作区 -->
      <template #extra>
        <n-button type="primary">New Entry</n-button>
      </template>
    </n-page-header>

    <div class="view-content">
      <!-- 状态显示 -->
       <n-spin v-if="uiStore.isLoading" description="Loading entries..." size="large" />
      <n-empty
        v-else-if="uiStore.error"
        description="Failed to load data"
      >
        <template #extra>
          <p>{{ uiStore.error }}</p>
          <n-button @click="fetchData" type="primary">Retry</n-button>
        </template>
      </n-empty>
      <n-empty
        v-else-if="!entries || entries.length === 0"
        description="This worldbook has no entries."
      >
        <template #extra>
          <n-button type="primary">Create the First Entry</n-button>
        </template>
      </n-empty>
      
      <!-- 条目列表 -->
      <n-collapse v-else accordion>
        <n-collapse-item v-for="entry in entries" :key="entry.id">
            <template #header>
                <div class="entry-header">
                    <span>{{ entry.keys.join(', ') || 'Untitled Entry' }}</span>
                </div>
            </template>
            <template #header-extra>
                <div class="entry-actions">
                    <n-button text><n-icon :component="EditIcon" /></n-button>
                    <n-button text style="margin-left: 8px;"><n-icon :component="DeleteIcon" /></n-button>
                </div>
            </template>

            <div class="entry-content">
                <pre>{{ entry.content }}</pre>
            </div>
        </n-collapse-item>
      </n-collapse>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import {
  NPageHeader,
  NEmpty,
  NSpin,
  NCollapse,
  NCollapseItem,
  NButton,
  NIcon,
} from 'naive-ui';
import { useUIStateStore } from '../store/useUIStateStore';
import { useWorldbookStore } from '../store/useWorldbookStore';
import {
    CreateOutline as EditIcon,
    TrashOutline as DeleteIcon,
} from '@vicons/ionicons5';


const props = defineProps<{
  name: string;
}>();

const uiStore = useUIStateStore();
const worldbookStore = useWorldbookStore();
const router = useRouter();

const entries = computed(() => worldbookStore.currentWorldbook?.entries || []);

const fetchData = () => {
  if (props.name) {
    worldbookStore.fetchWorldbookDetail(props.name);
  } else {
    uiStore.setError('No worldbook name provided in route.');
  }
};

// 组件挂载时自动获取数据
onMounted(fetchData);
</script>

<style scoped>
.worldbook-detail-view {
  padding: 8px;
}
.view-content {
  padding-top: 16px;
}
.n-spin, .n-empty {
  height: 300px;
  display: flex;
  justify-content: center;
  align-items: center;
}
.entry-header {
    display: flex;
    align-items: center;
    flex-grow: 1;
}
.entry-actions {
    display: flex;
    align-items: center;
}
.entry-content {
    white-space: pre-wrap;
    word-break: break-all;
    padding: 0 16px;
}
</style>