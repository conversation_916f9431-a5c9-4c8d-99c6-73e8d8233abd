import { createRouter, createMemoryHistory } from 'vue-router';
import WorldbookListView from './views/WorldbookListView.vue';
import WorldbookDetailView from './views/WorldbookDetailView.vue';

const routes = [
  { path: '/', name: 'list', component: WorldbookListView },
  // 路由参数 `:name` 用于传递 worldbook 名称
  { path: '/detail/:name', name: 'detail', component: WorldbookDetailView, props: true },
];

const router = createRouter({
  history: createMemoryHistory(),
  routes,
});

export default router;